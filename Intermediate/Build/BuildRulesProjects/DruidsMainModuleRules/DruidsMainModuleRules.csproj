<!-- This file was generated by UnrealBuildTool.ProjectFileGenerator.CreateRulesAssemblyProject() -->
<Project Sdk="Microsoft.NET.Sdk">
  <Import Project="C:\Program Files\Epic Games\UE_5.4\Engine\Source\Programs\Shared\UnrealEngine.csproj.props" />
  <PropertyGroup>
    <TargetFramework>net6.0</TargetFramework>
    <AppendTargetFrameworkToOutputPath>false</AppendTargetFrameworkToOutputPath>
    <Configurations>Debug;Release;Development</Configurations>
    <DefineConstants>$(DefineConstants);WITH_FORWARDED_MODULE_RULES_CTOR;WITH_FORWARDED_TARGET_RULES_CTOR;UE_4_17_OR_LATER;UE_4_18_OR_LATER;UE_4_19_OR_LATER;UE_4_20_OR_LATER;UE_4_21_OR_LATER;UE_4_22_OR_LATER;UE_4_23_OR_LATER;UE_4_24_OR_LATER;UE_4_25_OR_LATER;UE_4_26_OR_LATER;UE_4_27_OR_LATER;UE_4_28_OR_LATER;UE_4_29_OR_LATER;UE_4_30_OR_LATER;UE_5_0_OR_LATER;UE_5_1_OR_LATER;UE_5_2_OR_LATER;UE_5_3_OR_LATER;UE_5_4_OR_LATER</DefineConstants>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="C:\Program Files\Epic Games\UE_5.4\Engine\Intermediate\Build\BuildRulesProjects\MarketplaceRules\MarketplaceRules.csproj"><Private>false</Private></ProjectReference>
    <ProjectReference Include="C:\Program Files\Epic Games\UE_5.4\Engine\Intermediate\Build\BuildRulesProjects\UE5ProgramRules\UE5ProgramRules.csproj"><Private>false</Private></ProjectReference>
    <ProjectReference Include="C:\Program Files\Epic Games\UE_5.4\Engine\Intermediate\Build\BuildRulesProjects\UE5Rules\UE5Rules.csproj"><Private>false</Private></ProjectReference>
    <ProjectReference Include="C:\Program Files\Epic Games\UE_5.4\Engine\Source\Programs\Shared\EpicGames.Build\EpicGames.Build.csproj"><Private>false</Private></ProjectReference>
    <ProjectReference Include="C:\Program Files\Epic Games\UE_5.4\Engine\Source\Programs\UnrealBuildTool\UnrealBuildTool.csproj"><Private>false</Private></ProjectReference>
  </ItemGroup>
  <ItemGroup>
  <Compile Include="..\..\..\..\Plugins\Developer\RiderLink\Source\RD\RD.Build.cs"><Link>Plugins\Developer\RiderLink\Source\RD\RD.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\RiderLink\Source\RiderBlueprint\RiderBlueprint.Build.cs"><Link>Plugins\Developer\RiderLink\Source\RiderBlueprint\RiderBlueprint.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\RiderLink\Source\RiderDebuggerSupport\RiderDebuggerSupport.Build.cs"><Link>Plugins\Developer\RiderLink\Source\RiderDebuggerSupport\RiderDebuggerSupport.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\RiderLink\Source\RiderGameControl\RiderGameControl.Build.cs"><Link>Plugins\Developer\RiderLink\Source\RiderGameControl\RiderGameControl.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\RiderLink\Source\RiderLC\RiderLC.Build.cs"><Link>Plugins\Developer\RiderLink\Source\RiderLC\RiderLC.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\RiderLink\Source\RiderLink\RiderLink.Build.cs"><Link>Plugins\Developer\RiderLink\Source\RiderLink\RiderLink.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\RiderLink\Source\RiderLogging\RiderLogging.Build.cs"><Link>Plugins\Developer\RiderLink\Source\RiderLogging\RiderLogging.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Developer\RiderLink\Source\RiderShaderInfo\RiderShaderInfo.Build.cs"><Link>Plugins\Developer\RiderLink\Source\RiderShaderInfo\RiderShaderInfo.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Druids\Source\DruidsCore\DruidsCore.Build.cs"><Link>Plugins\Druids\Source\DruidsCore\DruidsCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Druids\Source\DruidsHugh\DruidsHugh.Build.cs"><Link>Plugins\Druids\Source\DruidsHugh\DruidsHugh.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Druids\Source\DruidsSage\Features\SageContext\SageContext.Build.cs"><Link>Plugins\Druids\Source\DruidsSage\Features\SageContext\SageContext.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Druids\Source\DruidsSage\Features\SageExtensions\SageExtensions.Build.cs"><Link>Plugins\Druids\Source\DruidsSage\Features\SageExtensions\SageExtensions.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Druids\Source\DruidsSage\Features\SagePython\SagePython.Build.cs"><Link>Plugins\Druids\Source\DruidsSage\Features\SagePython\SagePython.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Druids\Source\DruidsSage\Foundation\SageCommonTypes\SageCommonTypes.Build.cs"><Link>Plugins\Druids\Source\DruidsSage\Foundation\SageCommonTypes\SageCommonTypes.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Druids\Source\DruidsSage\Foundation\SageCore\SageCore.Build.cs"><Link>Plugins\Druids\Source\DruidsSage\Foundation\SageCore\SageCore.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Druids\Source\DruidsSage\Interaction\DruidsSageEditorModule\DruidsSageEditorModule.Build.cs"><Link>Plugins\Druids\Source\DruidsSage\Interaction\DruidsSageEditorModule\DruidsSageEditorModule.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Druids\Source\DruidsSage\Interaction\SageNetworking\SageNetworking.Build.cs"><Link>Plugins\Druids\Source\DruidsSage\Interaction\SageNetworking\SageNetworking.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Druids\Source\DruidsSage\Interaction\SageUI\SageUI.Build.cs"><Link>Plugins\Druids\Source\DruidsSage\Interaction\SageUI\SageUI.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Druids\Source\DruidsSage\SageMain\SageMain.Build.cs"><Link>Plugins\Druids\Source\DruidsSage\SageMain\SageMain.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Druids\Source\DruidsSage\Utilities\SageActorUtilities\SageActorUtilities.Build.cs"><Link>Plugins\Druids\Source\DruidsSage\Utilities\SageActorUtilities\SageActorUtilities.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Druids\Source\DruidsSage\Utilities\SageBlueprintUtilities\SageBlueprintUtilities.Build.cs"><Link>Plugins\Druids\Source\DruidsSage\Utilities\SageBlueprintUtilities\SageBlueprintUtilities.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Druids\Source\DruidsSage\Utilities\SageDocuments\SageDocuments.Build.cs"><Link>Plugins\Druids\Source\DruidsSage\Utilities\SageDocuments\SageDocuments.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Plugins\Druids\Source\DruidsSage\Utilities\SageJSONUtilities\SageJSONUtilities.Build.cs"><Link>Plugins\Druids\Source\DruidsSage\Utilities\SageJSONUtilities\SageJSONUtilities.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\DruidsMain.Target.cs"><Link>Source\DruidsMain.Target.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\DruidsMainEditor.Target.cs"><Link>Source\DruidsMainEditor.Target.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\DruidsMainModule\DruidsMainModule.Build.cs"><Link>Source\DruidsMainModule\DruidsMainModule.Build.cs</Link></Compile>
  <Compile Include="..\..\..\..\Source\NiagaraToolsModule\NiagaraToolsModule.Build.cs"><Link>Source\NiagaraToolsModule\NiagaraToolsModule.Build.cs</Link></Compile>
  </ItemGroup>
</Project>
