#include "ActorSearchUtilities.h"
#include "Engine/World.h"
#include "Engine/Engine.h"
#include "LogDruids.h"

TArray<FActorSearchResultItem> UActorSearchUtilities::FindActorsByName(const FString& SearchQuery, const UObject* WorldContext)
{
    TArray<FActorSearchResultItem> Results;

    if (SearchQuery.IsEmpty())
    {
        UE_LOG(LogDruidsSage, Warning, TEXT("Empty search query provided for Actor search."));
        return Results;
    }

    // Get the world from the context
    UWorld* World = nullptr;
    if (WorldContext)
    {
        World = WorldContext->GetWorld();
    }
    
    if (!World)
    {
        UE_LOG(LogDruidsSage, Warning, TEXT("No valid world context provided for Actor search."));
        return Results;
    }

    // Parse search query into individual terms
    TArray<FString> SearchTerms;
    SearchQuery.ParseIntoArray(SearchTerms, TEXT(" "), true);

    if (SearchTerms.Num() == 0)
    {
        return Results;
    }

    // Convert search terms to lowercase for case-insensitive search
    for (FString& Term : SearchTerms)
    {
        Term = Term.ToLower();
    }

    // Get all actors in the world
    TArray<FActorSearchResultItem> AllResults;
    for (TActorIterator<AActor> ActorIterator(World); ActorIterator; ++ActorIterator)
    {
        AActor* Actor = *ActorIterator;
        if (!Actor || !IsValid(Actor))
        {
            continue;
        }

        FString ActorName = Actor->GetName();
        float Score = CalculateRelevanceScore(ActorName, SearchTerms);

        if (Score > 0.0f)
        {
            AllResults.Add(FActorSearchResultItem(Actor, Score));
        }
    }

    // Sort by relevance score (highest first)
    AllResults.Sort([](const FActorSearchResultItem& A, const FActorSearchResultItem& B)
    {
        return A.RelevanceScore > B.RelevanceScore;
    });

    // Return top 10 results
    int32 MaxResults = FMath::Min(10, AllResults.Num());
    for (int32 i = 0; i < MaxResults; i++)
    {
        Results.Add(AllResults[i]);
    }

    return Results;
}

float UActorSearchUtilities::CalculateRelevanceScore(const FString& ActorName, const TArray<FString>& SearchTerms)
{
    if (ActorName.IsEmpty() || SearchTerms.Num() == 0)
    {
        return 0.0f;
    }

    FString LowerActorName = ActorName.ToLower();
    TArray<FString> LowerSearchTerms;
    for (const FString& Term : SearchTerms)
    {
        LowerSearchTerms.Add(Term.ToLower());
    }

    float Score = 0.0f;

    // Check for exact match in order (highest score)
    if (IsExactMatch(LowerActorName, LowerSearchTerms))
    {
        Score = 100.0f;
    }
    // Check if all terms are present (any order)
    else if (ContainsAllTerms(LowerActorName, LowerSearchTerms))
    {
        Score = 80.0f;
    }
    // Partial matches get lower scores
    else
    {
        int32 MatchingTerms = CountMatchingTerms(LowerActorName, LowerSearchTerms);
        if (MatchingTerms > 0)
        {
            // Score based on percentage of matching terms
            float MatchPercentage = static_cast<float>(MatchingTerms) / static_cast<float>(LowerSearchTerms.Num());
            Score = 60.0f * MatchPercentage;
        }
    }

    // Apply length penalty for longer names (shorter names are more relevant)
    if (Score > 0.0f)
    {
        float LengthPenalty = FMath::Max(0.1f, 1.0f - (LowerActorName.Len() / 100.0f));
        Score *= LengthPenalty;
    }

    return Score;
}

bool UActorSearchUtilities::IsExactMatch(const FString& ActorName, const TArray<FString>& SearchTerms)
{
    if (SearchTerms.Num() == 0)
    {
        return false;
    }

    // Build the search string by joining terms with spaces
    FString SearchString;
    for (int32 i = 0; i < SearchTerms.Num(); i++)
    {
        if (i > 0)
        {
            SearchString += TEXT(" ");
        }
        SearchString += SearchTerms[i];
    }

    return ActorName.Contains(SearchString);
}

bool UActorSearchUtilities::ContainsAllTerms(const FString& ActorName, const TArray<FString>& SearchTerms)
{
    for (const FString& Term : SearchTerms)
    {
        if (!ActorName.Contains(Term))
        {
            return false;
        }
    }
    return true;
}

int32 UActorSearchUtilities::CountMatchingTerms(const FString& ActorName, const TArray<FString>& SearchTerms)
{
    int32 Count = 0;
    for (const FString& Term : SearchTerms)
    {
        if (ActorName.Contains(Term))
        {
            Count++;
        }
    }
    return Count;
}
