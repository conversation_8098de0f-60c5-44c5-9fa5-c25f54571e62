#include "SageActorUtilitiesModule.h"
#include "LogDruids.h"
#include "AssetRegistry/AssetRegistryModule.h"

#define LOCTEXT_NAMESPACE "FSageActorUtilitiesModule"

void FSageActorUtilitiesModule::StartupModule()
{
    UE_LOG(LogDruidsSage, Display, TEXT("SageActorUtilities module starting up..."));
    
    // Register for post-engine init to ensure all systems are ready
    FCoreDelegates::OnPostEngineInit.AddRaw(this, &FSageActorUtilitiesModule::OnPostEngineInit);
}

void FSageActorUtilitiesModule::ShutdownModule()
{
    UE_LOG(LogDruidsSage, Display, TEXT("SageActorUtilities module shutting down..."));
    
    // Clean up delegates
    FCoreDelegates::OnPostEngineInit.RemoveAll(this);
}

void FSageActorUtilitiesModule::OnPostEngineInit()
{
    UE_LOG(LogDruidsSage, Display, TEXT("SageActorUtilities post-engine init..."));
    
    // Check if asset registry is already loaded
    FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
    IAssetRegistry& AssetRegistry = AssetRegistryModule.Get();
    
    if (AssetRegistry.IsLoadingAssets())
    {
        // Asset registry is still loading, wait for it to complete
        AssetRegistry.OnFilesLoaded().AddRaw(this, &FSageActorUtilitiesModule::OnAssetRegistryLoaded);
    }
    else
    {
        // Asset registry is already loaded
        OnAssetRegistryLoaded();
    }
}

void FSageActorUtilitiesModule::OnAssetRegistryLoaded()
{
    FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
    IAssetRegistry& AssetRegistry = AssetRegistryModule.Get();
    AssetRegistry.OnFilesLoaded().RemoveAll(this);

    UE_LOG(LogDruidsSage, Display, TEXT("SageActorUtilities asset registry loaded."));
}

#undef LOCTEXT_NAMESPACE

IMPLEMENT_MODULE(FSageActorUtilitiesModule, SageActorUtilities)
