#pragma once

#include "CoreMinimal.h"
#include "Kismet/BlueprintFunctionLibrary.h"
#include "Engine/World.h"
#include "ActorSearchUtilities.generated.h"

/**
 * Struct for Actor search result item
 */
USTRUCT(BlueprintType)
struct SAGEACTORUTILITIES_API FActorSearchResultItem
{
    GENERATED_BODY()

    FActorSearchResultItem()
        : Actor(nullptr)
        , RelevanceScore(0.0f)
    {
    }

    FActorSearchResultItem(AActor* InActor, float InRelevanceScore)
        : Actor(InActor)
        , RelevanceScore(InRelevanceScore)
    {
    }

    /** The Actor reference */
    UPROPERTY(BlueprintReadOnly, Category = "Actor Search")
    TObjectPtr<AActor> Actor;

    /** The relevance score for this result */
    UPROPERTY(BlueprintReadOnly, Category = "Actor Search")
    float RelevanceScore;
};

/**
 * Blueprint function library for Actor search utilities
 */
UCLASS()
class SAGEACTORUTILITIES_API UActorSearchUtilities : public UBlueprintFunctionLibrary
{
    GENERATED_BODY()

public:
    /** 
     * Finds Actors by name using a search query
     * @param SearchQuery - Space-separated list of words to search for
     * @param WorldContext - World context object to get the world from
     * @return Array of search results sorted by relevance (top 10 results)
     */
    UFUNCTION(BlueprintCallable, Category = "Actor Search", meta = (WorldContext = "WorldContext"))
    static TArray<FActorSearchResultItem> FindActorsByName(const FString& SearchQuery, const UObject* WorldContext);

private:
    /** 
     * Calculates the relevance score for an Actor name against search terms
     * @param ActorName - The name of the Actor to score
     * @param SearchTerms - Array of search terms
     * @return Relevance score (0.0 to 100.0)
     */
    static float CalculateRelevanceScore(const FString& ActorName, const TArray<FString>& SearchTerms);

    /** 
     * Checks if all search terms are found in the Actor name in the exact order
     * @param ActorName - The name to check
     * @param SearchTerms - Array of search terms
     * @return True if exact match found
     */
    static bool IsExactMatch(const FString& ActorName, const TArray<FString>& SearchTerms);

    /** 
     * Checks if all search terms are found in the Actor name (any order)
     * @param ActorName - The name to check
     * @param SearchTerms - Array of search terms
     * @return True if all terms are found
     */
    static bool ContainsAllTerms(const FString& ActorName, const TArray<FString>& SearchTerms);

    /** 
     * Counts how many search terms are found in the Actor name
     * @param ActorName - The name to check
     * @param SearchTerms - Array of search terms
     * @return Number of terms found
     */
    static int32 CountMatchingTerms(const FString& ActorName, const TArray<FString>& SearchTerms);
};
