using UnrealBuildTool;

public class SageActorUtilities : ModuleRules
{
    public SageActorUtilities(ReadOnlyTargetRules Target) : base(Target)
    {
        PCHUsage = ModuleRules.PCHUsageMode.UseExplicitOrSharedPCHs;

        PublicDefinitions.Add("SAGEACTORUTILITIES_API=DLLEXPORT");

        PublicDependencyModuleNames.AddRange(
            new string[]
            {
                "Core",
            }
        );

        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "CoreUObject",
                "Engine",
                "Slate",
                "SlateCore",
                "InputCore",
                "Json",
            }
        );

        // Add editor-specific dependencies only when building for editor
        if (Target.bBuildEditor == true)
        {
            PrivateDependencyModuleNames.AddRange(
                new string[]
                {
                    "UnrealEd",
                    "AssetRegistry",
                }
            );
        }

        PrivateDependencyModuleNames.AddRange(
            new string[]
            {
                "DruidsCore",
                
                "SageJSONUtilities",
            });
    }
}
